use std::collections::BTreeMap;
use std::fs;
use std::future::Future;
use std::io::Read;
use std::os::unix::fs::MetadataExt;
use std::path::{Path, PathBuf};
use std::pin::Pin;
use std::thread::sleep;
use std::time::Duration;
use agent_db::config::runtime_config::AGENT_CONFIG;
use futures::FutureExt;
use log::info;
use serde_json::{json, Value};
use sha1::{Digest, Sha1};
use crate::utils::parallel_file_scan::CHAT_INDEX_SKIP_ARR;
use crate::utils::path::{count_content_line, get_all_file_by_url};

#[derive(Debug, <PERSON>lone, Copy)]
enum FileType {
    Blob,
    BlobExe,
    Tree,
    Symlink,
}

impl FileType {
    fn as_str(&self) -> &'static str {
        match self {
            FileType::Blob => "100644",
            FileType::BlobExe => "100755",
            FileType::Tree => "040000",
            FileType::Symlink => "120000",
        }
    }
}

fn load_gitignore_patterns(gitignore_path: &Path) -> Result<Vec<String>, Box<dyn std::error::Error>> {
    if !gitignore_path.is_file() {
        return Ok(Vec::new());
    }
    
    let content = fs::read_to_string(gitignore_path)?;
    Ok(content.lines()
        .map(|s| s.trim())
        .filter(|s| !s.is_empty() && !s.starts_with('#'))
        .map(|s| s.to_string())
        .collect())
}

fn is_ignored(patterns: &[String], rel_path: &str) -> bool {
    for pattern in patterns {
        // 简单的匹配：如果路径以模式开头，或完全匹配，则忽略
        if rel_path.starts_with(pattern.trim_end_matches('/')) || 
           rel_path == pattern.trim_end_matches('/') {
            return true;
        }
        
        // 通配符匹配 * 的简化版本
        if pattern.contains('*') {
            let simplified = pattern.replace('*', "");
            if !simplified.is_empty() && rel_path.contains(&simplified) {
                return true;
            }
        }
    }
    false
}

fn git_style_hash(content: &[u8], obj_type: &str) -> String {
    let header = format!("{} {}\0", obj_type, content.len());
    let mut hasher = Sha1::new();
    hasher.update(header.as_bytes());
    hasher.update(content);
    hex::encode(hasher.finalize())
}

fn hash_blob(file_path: &Path, mode: &str) -> Result<String, Box<dyn std::error::Error>> {
    let mut file = fs::File::open(file_path)?;
    let mut content = Vec::new();
    file.read_to_end(&mut content)?;
    Ok(format!("{}:{}", mode, git_style_hash(&content, "blob")))
}

fn mode_for_path(path: &Path) -> Result<String, Box<dyn std::error::Error>> {
    let metadata = fs::symlink_metadata(path)?;
    let file_type = metadata.file_type();
    
    if file_type.is_symlink() {
        Ok(FileType::Symlink.as_str().to_string())
    } else if file_type.is_file() {
        let mode = metadata.mode();
        if mode & 0o100 != 0 {
            Ok(FileType::BlobExe.as_str().to_string())
        } else {
            Ok(FileType::Blob.as_str().to_string())
        }
    } else if file_type.is_dir() {
        Ok(FileType::Tree.as_str().to_string())
    } else {
        Err(format!("Unsupported file type: {:?}", path).into())
    }
}

fn hash_tree(entries: &BTreeMap<String, (String, String)>) -> String {
    let mut tree_content = Vec::new();
    
    for (name, (mode, sha1_hex)) in entries {
        let clean_mode = mode.trim_start_matches('0');
        let sha1_raw = hex::decode(&sha1_hex.split(':').nth(1).unwrap()).unwrap();
        
        tree_content.extend_from_slice(clean_mode.as_bytes());
        tree_content.push(b' ');
        tree_content.extend_from_slice(name.as_bytes());
        tree_content.push(b'\0');
        tree_content.extend_from_slice(&sha1_raw);
    }
    
    format!("{}:{}", FileType::Tree.as_str(), git_style_hash(&tree_content, "tree"))
}

// 定义一个类型别名，方便使用
type PinnedBoxFuture<T> = Pin<Box<dyn Future<Output = T> + Send>>;
async fn build_hash_tree(root_dir: &PathBuf, base_patterns: &[String], sleep_time: usize) -> PinnedBoxFuture<Result<Value, Box<dyn std::error::Error>>> {
    info!("build_hash_tree root_dir {}", root_dir.to_string_lossy());
    let mut result = json!({});
    let mut entries = BTreeMap::new();

    // 当前目录下的 .gitignore
    let local_gitignore = Path::new(root_dir).join(".gitignore");
    let local_patterns = load_gitignore_patterns(&local_gitignore)?;
    let combined_patterns: Vec<String> = base_patterns.iter()
        .chain(local_patterns.iter())
        .cloned()
        .collect();

    let mut entries_list = Vec::new();
    for entry_item in fs::read_dir(root_dir)? {
        let entry = entry_item?;
        //复用之前的逻辑
        let file_type = entry.file_type();
        if file_type.is_err() {
            continue;
        }
        if file_type.unwrap().is_file() {
            let file_size = fs::metadata(entry.path()).unwrap().len();
            //过滤掉空文件
            if file_size == 0 {
                continue;
            }
            if file_size > AGENT_CONFIG.scan_skip_file_size {
                info!("chat index skip file: {}",entry.path().display().to_string());
                continue;
            }
            //超过指定大小的文件（40kb）, 要计算行数， 超过行数上限的文件要过滤掉
            if file_size > AGENT_CONFIG.file_size_to_calculate_line {
                let lines_count_rst = count_content_line(entry.path().to_str().unwrap());
                if let Ok(lines_count) = lines_count_rst {
                    if lines_count > AGENT_CONFIG.scan_skip_file_max_len {
                        info!("chat index skip file: {} since exceed max_line_count",entry.path().display().to_string());
                        continue;
                    }
                }
            }

            // let extension_opt = entry.path().extension();
            let path: PathBuf = entry.path();
            let extension_opt = path.extension();
            if let None = extension_opt {
                continue;
            }
            //let path_str = entry.path().to_string_lossy();
            let path_str = path.to_string_lossy();
            if path_str.contains("src/test") || path_str.contains("src\\test") {
                continue;
            }

            let file_name_suffix = extension_opt.unwrap().to_string_lossy().to_string();

            if !(AGENT_CONFIG.similarity_suffix_arr.contains(&file_name_suffix) || AGENT_CONFIG.related_suffix_arr.contains(&file_name_suffix)) {
                continue;
            }

            if CHAT_INDEX_SKIP_ARR.contains(&file_name_suffix) {
                continue;
            }
        }
        //复用之前的逻辑end

        let name = entry.file_name().into_string().unwrap();
        let full_path = entry.path();
        let rel_path = full_path.strip_prefix(root_dir).unwrap().to_string_lossy().into_owned();

        if name == ".git" || name == ".ruff_cache" {
            continue;
        }

        if is_ignored(&combined_patterns, &rel_path) {
            continue;
        }

        entries_list.push((name, full_path, rel_path));
    }
    
    entries_list.sort_by(|a, b| a.0.cmp(&b.0));

    for (name, full_path, _rel_path) in entries_list {
        let metadata = fs::symlink_metadata(&full_path)?;
        let file_type = metadata.file_type();

        if file_type.is_symlink() {
            let mode = FileType::Symlink.as_str().to_string();
            let blob_hash = hash_blob(&full_path, &mode)?;
            result[&name] = json!(blob_hash);
            entries.insert(name, (mode, blob_hash));
        } else if file_type.is_dir() {
            // let sub_tree = build_hash_tree(&full_path, &combined_patterns, sleep_time).await?;
            let sub_tree = build_hash_tree(&full_path, &combined_patterns, sleep_time).await.boxed();
            let tree_hash = sub_tree["__tree_hash__"].as_str().unwrap().to_string();
            
            let mut sub_tree_obj = sub_tree.as_object().unwrap().clone();
            sub_tree_obj.insert("__tree_hash__".to_string(), json!(tree_hash.clone()));
            
            result[&name] = json!(sub_tree_obj);
            entries.insert(name, (FileType::Tree.as_str().to_string(), tree_hash));
        } else if file_type.is_file() {
            let mode = mode_for_path(&full_path)?;
            let blob_hash = hash_blob(&full_path, &mode)?;
            result[&name] = json!(blob_hash);
            entries.insert(name, (mode, blob_hash));
            tokio::time::sleep(Duration::from_millis(sleep_time as u64)).await;
        }
    }

    let tree_hash = hash_tree(&entries);
    let mut result_obj = result.as_object().unwrap().clone();
    result_obj.insert("__tree_hash__".to_string(), json!(tree_hash));
    
    Ok(json!(result_obj))
}

fn git_output(data: &Value, prefix: &str) -> Vec<String> {
    let mut lines = Vec::new();
    
    if let Some(obj) = data.as_object() {
        let mut keys: Vec<_> = obj.keys().collect();
        keys.sort();
        
        for name in keys {
            if name == "__tree_hash__" || name == "__mode__" || name == "__hash__" {
                continue;
            }
            
            let full_path = if prefix.is_empty() {
                name.clone()
            } else {
                format!("{}/{}", prefix, name)
            };
            
            if let Some(value) = obj.get(name) {
                if let Some(sub_obj) = value.as_object() {
                    if let Some(tree_hash) = sub_obj.get("__tree_hash__") {
                        let parts: Vec<&str> = tree_hash.as_str().unwrap().split(':').collect();
                        if parts.len() == 2 {
                            let mode = parts[0];
                            let hash = parts[1];
                            lines.push(format!("{} tree {}\t{}", mode, hash, full_path));
                            lines.extend(git_output(value, &full_path));
                        }
                    }
                } else if let Some(blob_hash) = value.as_str() {
                    let parts: Vec<&str> = blob_hash.split(':').collect();
                    if parts.len() == 2 {
                        let mode = parts[0];
                        let hash = parts[1];
                        lines.push(format!("{} blob {}\t{}", mode, hash, full_path));
                    }
                }
            }
        }
    }
    
    lines
}

//生成merkle tree
pub async fn build_merkle_tree(root_dir: &String, sleep_time: usize) -> Result<Value, Box<dyn std::error::Error>> {
    info!("currnet root_dir {}", root_dir);
    let target_path = PathBuf::from(root_dir);
    // 从根目录开始加载 .gitignore
    let root_gitignore = target_path.join(".gitignore");
    let base_patterns = load_gitignore_patterns(&root_gitignore)?;
    let tree = build_hash_tree(&target_path, &base_patterns, sleep_time).await?;
    info!("{}", serde_json::to_string_pretty(&tree)?);
    Ok(tree)
}